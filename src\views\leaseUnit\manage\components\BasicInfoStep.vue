<template>
  <div>
    <h2 class="text-[16px] font-bold my-[24px]">租赁单元基础信息</h2>
    <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 4 }">
      <a-form-item label="单元名称" name="name" required>
        <a-input v-model:value="formData.name" placeholder="请输入单元名称" show-count :maxlength="50" />
      </a-form-item>
      <a-form-item label="资产产权" name="virtualLeaseUnit">
        <a-radio-group v-model:value="formData.virtualLeaseUnit">
          <a-radio :value="false">无产权</a-radio>
          <a-radio :value="true">有产权</a-radio>
        </a-radio-group>
        <house-owner-selector
          v-if="formData.virtualLeaseUnit === true"
          v-model="formData.houseOwner"
          v-model:display-value="formData.houseOwner_dictText"
          placeholder="选择房屋"
          :style="{ width: '200px' }"
          @change="handleHouseOwnerChange"
        />
      </a-form-item>
      <a-form-item label="产权用途" name="propertyUse">
        <dict-select v-model="formData.propertyUse" placeholder="产权用途" code="CT_BAS_PropertyUse"></dict-select>
      </a-form-item>
      <a-form-item label="所属项目" name="wyProject">
        <a-cascader
          v-model:value="formData.wyProjectArray"
          :options="projectOptions"
          :field-names="{ label: 'label', value: 'value', children: 'children' }"
          :load-data="loadBuildingFloorData"
          placeholder="请选择项目/楼栋/楼层"
          @change="handleProjectChange"
        />
      </a-form-item>
      <a-form-item label="地址" required>
        <a-form-item name="pcaCodeArray" :no-style="true">
          <a-cascader
            v-model:value="formData.pcaCodeArray"
            placeholder="选择省市区"
            class="pca-code"
            :options="region"
            @change="handlePcaCodeChange"
            style="width: 50%"
          />
        </a-form-item>
        <a-form-item name="detailAddress" :no-style="true">
          <a-input
            v-model:value="formData.detailAddress"
            placeholder="请输入详细地址"
            class="detail-address"
            style="width: 50%"
          />
        </a-form-item>
      </a-form-item>
      <a-form-item label="资产类型" name="assetType" required>
        <dict-select
          v-model="formData.assetType"
          placeholder="资产类型"
          code="CT_BASE_ENUM_LeaseUnit_AssetType"
        ></dict-select>
      </a-form-item>
      <a-form-item label="资产权属公司" name="ownerCompany" required>
        <dept-tree-select
          v-model="formData.ownerCompany"
          placeholder="请选择资产权属公司"
          type="company"
        ></dept-tree-select>
      </a-form-item>
      <a-form-item label="租金归集公司" name="collectionCompany" required>
        <dept-tree-select
          v-model="formData.collectionCompany"
          placeholder="请选择租金归集公司"
          type="company"
        ></dept-tree-select>
      </a-form-item>
      <a-form-item label="物业管理公司" name="manageCompany" required>
        <dept-tree-select
          v-model="formData.manageCompany"
          placeholder="请选择物业管理公司"
          type="company"
        ></dept-tree-select>
      </a-form-item>
      <a-form-item label="使用权类型" name="landNature" required>
        <dict-select v-model="formData.landNature" placeholder="使用权类型" code="CT_BAS_LandNature"></dict-select>
      </a-form-item>
      <a-form-item label="租赁单元类别" name="treeId" required>
        <api-tree-select
          v-model="formData.treeId"
          :async-fn="getLeaseUnitTree"
          placeholder="请选择租赁单元类别"
        ></api-tree-select>
      </a-form-item>
      <a-form-item label="配套设施" name="supportFacility" :label-col="{ span: 2 }" class="form-item-full">
        <a-textarea
          v-model:value="formData.supportFacility"
          placeholder="配套设施"
          :rows="4"
          show-count
          :maxlength="500"
        />
      </a-form-item>
      <a-form-item label="备注" name="remark" :label-col="{ span: 2 }" class="form-item-full">
        <a-textarea v-model:value="formData.remark" placeholder="备注" :rows="4" show-count :maxlength="500" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import region from '@/json/region.json'
import { projectPage, queryBuilding } from '@/views/projects/apis.js'
import { queryFloor } from '@/views/building/apis/building.js'
import { getLeaseUnitTree } from '../apis/leaseUnitTree'
import HouseOwnerSelector from './HouseOwnerSelector.vue'

const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  rules: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['houseOwnerChange', 'pcaCodeChange', 'projectChange'])

const formRef = ref()
const projectOptions = ref([])

/**
 * 处理资产选择变更
 * @param {Object} selectItem - 选中的资产对象
 */
const handleHouseOwnerChange = (selectItem) => {
  emit('houseOwnerChange', selectItem)
}

/**
 * 处理省市区选择变更
 * @param {Array} value - 选中的省市区代码数组
 * @param {Array} selectedOptions - 选中的省市区选项数组
 */
const handlePcaCodeChange = (value, selectedOptions) => {
  emit('pcaCodeChange', value, selectedOptions)
}

/**
 * 处理项目楼栋楼层选择变更
 * @param {Array} value - 选中的项目楼栋楼层ID数组
 */
const handleProjectChange = (value) => {
  emit('projectChange', value)
}

/**
 * 级联加载楼栋和楼层数据
 * @param {Array} selectedOptions - 级联选择的选项数组
 */
const loadBuildingFloorData = async (selectedOptions) => {
  const targetOption = selectedOptions[selectedOptions.length - 1]
  targetOption.loading = true

  try {
    const isLoadingBuilding = selectedOptions.length === 1
    const api = isLoadingBuilding ? queryBuilding : queryFloor
    const res = await api({ id: targetOption.value })

    if (res?.result) {
      targetOption.children = res.result.map((item) => ({
        value: item.id,
        label: item.name,
        isLeaf: !isLoadingBuilding
      }))
    }
  } finally {
    targetOption.loading = false
  }
}

/**
 * 加载项目列表数据
 */
const loadProjectList = async () => {
  const { result } = await projectPage()
  projectOptions.value =
    result?.records?.map((item) => ({
      value: item.id,
      label: item.name,
      isLeaf: false
    })) || []

  // 如果有选中的项目，重新加载项目数据
  if (props.formData.wyProject) {
    await initProjectData()
  }
}

/**
 * 初始化项目数据
 */
const initProjectData = async () => {
  // 加载楼栋数据
  if (props.formData.wyProject) {
    const buildingRes = await queryBuilding({ id: props.formData.wyProject })
    const projectOption = projectOptions.value.find((item) => item.value === props.formData.wyProject)

    if (projectOption && buildingRes?.result) {
      projectOption.children = buildingRes.result.map((item) => ({
        value: item.id,
        label: item.name,
        isLeaf: false
      }))

      // 加载楼层数据
      if (props.formData.wyBuilding) {
        const floorRes = await queryFloor({ id: props.formData.wyBuilding })
        const buildingOption = projectOption.children.find((item) => item.value === props.formData.wyBuilding)

        if (buildingOption && floorRes?.result) {
          buildingOption.children = floorRes.result.map((item) => ({
            value: item.id,
            label: item.name,
            isLeaf: true
          }))
        }
      }
    }
  }

  // 设置选中值
  nextTick(() => {
    props.formData.wyProjectArray = [
      props.formData.wyProject,
      props.formData.wyBuilding,
      props.formData.wyFloor
    ].filter(Boolean)
  })
}

/**
 * 验证表单
 */
const validate = () => {
  return formRef.value?.validate()
}

/**
 * 重置表单
 */
const resetFields = () => {
  formRef.value?.resetFields()
}

// 监听产权状态变化，清空相关字段
watch(
  () => props.formData.virtualLeaseUnit,
  (newVal, oldVal) => {
    if (oldVal === true && newVal === false) {
      props.formData.houseOwner = ''
      props.formData.houseOwner_dictText = undefined
    }
  }
)

// 组件挂载时加载项目列表
onMounted(() => {
  loadProjectList()
})

defineExpose({
  validate,
  resetFields,
  initProjectData
})
</script>

<style scoped lang="less">
.pca-code {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: 0;
}

.detail-address {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
</style>
