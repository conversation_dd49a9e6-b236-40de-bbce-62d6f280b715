<template>
  <div>
    <h2 class="text-[16px] font-bold my-[24px]">联系信息</h2>
    <a-form ref="contactFormRef" :model="formData" :rules="contactRules" :label-col="{ span: 4 }">
      <a-form-item label="联系人" name="linkman">
        <a-input v-model:value="formData.linkman" placeholder="请输入联系人" />
      </a-form-item>
      <a-form-item label="联系地址" name="contactAddress">
        <a-cascader
          v-model:value="formData.contactRegionArray"
          placeholder="选择区域"
          class="pca-code"
          :options="region"
          @change="handleContactRegionChange"
        />
        <a-input v-model:value="formData.contactDetailAddress" placeholder="请输入详细地址" class="detail-address" />
      </a-form-item>
      <a-form-item label="联系电话" name="linkmanPhone">
        <a-input v-model:value="formData.linkmanPhone" placeholder="请输入联系电话" />
      </a-form-item>
      <a-form-item label="推送手机" name="pushMobile">
        <a-input v-model:value="formData.pushMobile" placeholder="请输入推送手机" />
      </a-form-item>
      <a-form-item label="邮箱" name="ccEmail">
        <a-input v-model:value="formData.ccEmail" placeholder="请输入邮箱" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import region from '@/json/region.json'

const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

const contactFormRef = ref()

const contactRules = {
  linkmanPhone: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
  pushMobile: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
  ccEmail: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }]
}

/**
 * 处理联系地址选择变化
 */
const handleContactRegionChange = (value) => {
  props.formData.contactRegion = value.join(',')
  props.formData.contactRegionArray = value
}

/**
 * 验证联系信息表单
 */
const validate = async () => {
  await contactFormRef.value?.validate()
}

/**
 * 重置表单
 */
const resetFields = () => {
  contactFormRef.value?.resetFields()
}

defineExpose({
  validate,
  resetFields
})
</script>
