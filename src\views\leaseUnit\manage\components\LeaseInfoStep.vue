<template>
  <div>
    <h2 class="text-[16px] font-bold my-[24px]">租赁信息</h2>
    <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 5 }">
      <a-form-item label="使用类型" name="useType" required>
        <dict-select v-model="formData.useType" placeholder="使用类型" code="CT_BAS_UseType"></dict-select>
      </a-form-item>
      <a-form-item label="租赁面积(m²)" name="leaseArea" required>
        <a-input-number v-model:value="formData.leaseArea" :precision="2" style="width: 100%" addon-after="m²" />
      </a-form-item>
      <a-form-item label="租赁用途" name="leaseUse" required>
        <dict-select v-model="formData.leaseUse" placeholder="租赁用途" code="CT_BAS_LeaseUse"></dict-select>
      </a-form-item>
      <a-form-item label="片区管理员" name="areaManager">
        <user-select v-model="formData.areaManager" placeholder="请选择片区管理员" title="请选择片区管理员" />
      </a-form-item>
      <a-form-item label="生效日期" name="effectDate" required>
        <a-date-picker v-model:value="formData.effectDate" value-format="YYYY-MM-DD" style="width: 100%" />
      </a-form-item>
      <a-form-item label="到期日期" name="expireDate" required>
        <a-date-picker v-model:value="formData.expireDate" value-format="YYYY-MM-DD" style="width: 100%" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
defineProps({
  formData: {
    type: Object,
    required: true
  },
  rules: {
    type: Object,
    required: true
  }
})

const formRef = ref()

/**
 * 验证表单
 */
const validate = () => {
  return formRef.value?.validate()
}

/**
 * 重置表单
 */
const resetFields = () => {
  formRef.value?.resetFields()
}

defineExpose({
  validate,
  resetFields
})
</script>

<style scoped lang="less"></style>
