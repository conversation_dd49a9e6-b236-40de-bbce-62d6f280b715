<template>
  <div>
    <h2 class="text-[16px] font-bold my-[24px]">建筑信息</h2>
    <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 5 }">
      <a-form-item label="房产类型" name="houseType">
        <dict-select
          v-model="formData.houseType"
          placeholder="房产类型"
          code="CT_BASE_ENUM_HouseOwner_HouseType"
        ></dict-select>
      </a-form-item>
      <a-form-item label="建筑面积(m²)" name="structureArea" required>
        <a-input-number v-model:value="formData.structureArea" :precision="2" style="width: 100%" addon-after="m²" />
      </a-form-item>
      <a-form-item label="宗地面积(m²)" name="floorArea">
        <a-input-number v-model:value="formData.floorArea" :precision="2" style="width: 100%" addon-after="m²" />
      </a-form-item>
      <a-form-item label="建筑结构" name="buildStructrue" required>
        <dict-select
          v-model="formData.buildStructrue"
          placeholder="建筑结构"
          code="CT_BAS_BuildStructrue"
        ></dict-select>
      </a-form-item>
      <a-form-item label="建筑年份" name="buildYear">
        <a-date-picker v-model:value="formData.buildYear" picker="year" value-format="YYYY" style="width: 100%" />
      </a-form-item>
      <a-form-item label="层数/总层数" name="layerNum">
        <a-input-group compact>
          <a-input v-model:value="formData.currentLayer" style="width: 45%" placeholder="层数" />
          <a-input
            v-model:value="splitValue"
            class="site-input-split"
            style="width: 30px; border-left: 0; pointer-events: none"
            placeholder="/"
            disabled
          />
          <a-input
            v-model:value="formData.totalLayer"
            class="site-input-right"
            style="width: 45%"
            placeholder="总层数"
          />
        </a-input-group>
      </a-form-item>
      <a-form-item label="层高(m)" name="layerHight">
        <a-input-number v-model:value="formData.layerHight" :precision="2" style="width: 100%" addon-after="m" />
      </a-form-item>
      <a-form-item label="户型" name="houseModel">
        <a-input v-model:value="formData.houseModel" placeholder="户型" />
      </a-form-item>
      <a-form-item label="消防等级" name="firefightingRate">
        <dict-select
          v-model="formData.firefightingRate"
          placeholder="消防等级"
          code="CT_BAS_FirefightingRate"
        ></dict-select>
      </a-form-item>
      <a-form-item label="房屋安全等级" name="houseSafeRate" required>
        <dict-select
          v-model="formData.houseSafeRate"
          placeholder="房屋安全等级"
          code="CT_BAS_HouseSafeRate"
        ></dict-select>
      </a-form-item>
    </a-form>

    <h2 class="text-[16px] font-bold my-[24px]">税务信息</h2>
    <a-form :model="formData" :label-col="{ span: 5 }">
      <a-form-item label="房产税计税原值" name="houseTaxOrgValue">
        <a-input-number v-model:value="formData.houseTaxOrgValue" style="width: 100%" />
      </a-form-item>
      <a-form-item label="开票增值税率(%)" name="addTaxRate">
        <a-input-number v-model:value="formData.addTaxRate" style="width: 100%" addon-after="%" />
      </a-form-item>
      <a-form-item label="发票地址" name="invoiceAddress">
        <a-input v-model:value="formData.invoiceAddress" placeholder="发票地址" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  rules: {
    type: Object,
    required: true
  }
})

const formRef = ref()
const splitValue = ref('/')

/**
 * 验证表单
 */
const validate = () => {
  return formRef.value?.validate()
}

/**
 * 重置表单
 */
const resetFields = () => {
  formRef.value?.resetFields()
}

// 监听层数变化，自动拼接 layerNum
watch([() => props.formData.currentLayer, () => props.formData.totalLayer], ([currentLayer, totalLayer]) => {
  props.formData.layerNum = currentLayer || totalLayer ? `${currentLayer || ''}/${totalLayer || ''}` : ''
})

defineExpose({
  validate,
  resetFields
})
</script>

<style scoped lang="less">
.site-input-split {
  background-color: #fff;
  border-left: 0;
  pointer-events: none;
}

.site-input-right {
  border-left: 0;
}
</style>
