<template>
  <a-drawer v-model:open="visible" class="common-detail-drawer" width="1000px">
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 }"
            @click="handleSwitchDetail(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitchDetail(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span class="text-primary cursor-pointer mr-[16px]" @click="handleResend">重发</span>
        </div>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">{{ detailData.title }}</h2>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>{{ detailData.creator || '-' }} 发送于 {{ detailData.createTime || '-' }}</span>
      </div>
      <div id="basic" class="mb-[40px]">
        <h2 class="text-[16px] font-bold mb-[12px]">基础信息</h2>
        <div class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">消息标题：{{ detailData.title || '-' }}</span>
          <span class="w-[50%]">接收人：{{ detailData.recipient || '-' }}</span>
          <span class="w-[50%]">发送次数：{{ detailData.count || '-' }}</span>
          <span class="w-[50%]">发送状态：{{ detailData.status || '-' }}</span>
          <span class="w-[50%]">发送时间：{{ detailData.sendTime || '-' }}</span>
          <span class="w-[50%]">发送方式：{{ detailData.sendWay || '-' }}</span>
          <span class="w-[50%]">已读状态：{{ detailData.readFlag || '-' }}</span>
          <span class="w-[50%]">发送人：{{ detailData.creator || '-' }}</span>
        </div>
      </div>
      <h2 class="text-[16px] font-bold mb-[12px]">发送内容</h2>
      <div class="whitespace-pre-wrap">{{ detailData.templateContent }}</div>
    </a-spin>
  </a-drawer>
</template>

<script setup>
import { getMessageById, resendMessage } from '../apis'

const { dataList } = defineProps({
  dataList: { type: Array, required: true }
})

const visible = ref(false)
const loading = ref(false)

const detailData = ref({})

const currentIndex = computed(() => {
  if (!detailData.value.id) return 0
  return dataList.findIndex((i) => i.id === detailData.value.id)
})

/**
 * 打开详情抽屉
 * @param {String} id 消息ID
 */
const open = async (id) => {
  visible.value = true
  await loadDetail(id)
}

/**
 * 加载消息详情
 * @param {String} id 消息ID
 */
const loadDetail = async (id) => {
  loading.value = true
  try {
    const { result } = await getMessageById(id)
    detailData.value = result
  } finally {
    loading.value = false
  }
}

/**
 * 切换详情
 * @param {Number} index 索引
 */
const handleSwitchDetail = (index) => {
  if (index < 0 || index >= dataList.length) return
  const item = dataList[index]
  if (item) {
    loadDetail(item.id)
  }
}

/**
 * 重发消息
 */
const handleResend = async () => {
  if (!detailData.value.id) return

  try {
    loading.value = true
    await resendMessage(detailData.value.id)
    // 重新加载详情数据
    await loadDetail(detailData.value.id)
    // 这里可以添加成功提示，但根据项目规范，全局已配置异常捕获
  } finally {
    loading.value = false
  }
}

defineExpose({ open })
</script>
