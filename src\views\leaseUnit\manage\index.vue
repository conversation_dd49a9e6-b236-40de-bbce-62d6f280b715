<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex items-center justify-between mt-[24px] mb-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus mr-1"></i>
          新建
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right mr-1"></i>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <i class="a-icon-download mr-1"></i>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button>
            批量操作
            <i class="a-icon-arrow-down ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="1">批量设置水电计费规则</a-menu-item>
              <a-menu-item key="batchDelete" @click="handleBatchDelete">删除</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <lease-unit-tree-filter @treeNodeChange="handleTreeNodeChange" />
        <s-input
          v-model="searchParams.number"
          placeholder="搜索编号"
          class="ml-[40px] !w-[280px]"
          @input="handleSearch"
        ></s-input>
        <filter-more
          :params="searchParams"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :scroll="{ x: 1500, y: tableHeight }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'detailAddress'">
          {{ record.detailAddress }}
        </template>
        <template v-if="column.dataIndex === 'status'">
          <status-tag :dict-value="record.status" dict-code="CT_BASE_ENUM_BaseStatus" type="dot"></status-tag>
        </template>
        <template v-if="column.dataIndex === 'property'">
          <span class="primary-btn" @click="handleDetail(record)">查看</span>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleDetail(record)">查看</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item key="edit" @click="handleEdit(record)" v-if="record.status !== 'AUDITING'">
                  编辑
                </a-menu-item>
                <a-menu-item key="delete" @click="handleDelete(record)" v-if="record.status === 'TEMP'">
                  删除
                </a-menu-item>
                <a-menu-item key="unAudit" @click="handleUnAudit(record)" v-if="record.status === 'AUDITOK'">
                  反审批
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>

    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('租赁单元导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
    ></common-import>

    <edit-lease-unit ref="editDrawerRef" @refresh="onTableChange"></edit-lease-unit>
    <lease-unit-detail ref="detailDrawerRef" :data-list="list"></lease-unit-detail>
  </div>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import region from '@/json/region.json'
import EditLeaseUnit from './components/EditLeaseUnit.vue'
import LeaseUnitDetail from './components/LeaseUnitDetail.vue'
import { getUserList } from '@/views/system/user/apis'
import { projectPage } from '@/views/projects/apis.js'
import { getPage } from '@/views/assets/manage/apis'
import {
  getLeaseUnitList,
  deleteLeaseUnit,
  batchDeleteLeaseUnit,
  importExcel,
  exportExcel,
  unAudit
} from './apis/leaseUnit'
import LeaseUnitTreeFilter from './components/LeaseUnitTreeFilter.vue'

const route = useRoute()
const editDrawerRef = ref()
const detailDrawerRef = ref()
const commonImportRef = ref()
const columnSetRef = ref()
const exportLoading = ref(false)
let timer

const searchParams = reactive({
  name: undefined,
  pcaCode: [],
  useType: undefined,
  leaseUse: undefined,
  areaManager: undefined,
  status: undefined,
  bizStatus: undefined,
  supportFacility: undefined,
  effectDate: undefined,
  expireDate: undefined,
  layerNum: undefined,
  propertyUse: undefined,
  wyProject: undefined,
  number: undefined,
  treeId: undefined
})

const searchList = reactive([
  {
    label: '区域',
    name: 'pcaCode',
    type: 'cascader',
    placeholder: '请选择区域',
    options: region,
    fieldNames: { label: 'label', value: 'value', children: 'children' }
  },
  { label: '使用类型', name: 'useType', type: 'dict-select', placeholder: '请选择使用类型', code: 'CT_BAS_UseType' },
  { label: '租赁用途', name: 'leaseUse', type: 'dict-select', code: 'CT_BAS_LeaseUse', placeholder: '请选择租赁用途' },
  {
    label: '片区管理员',
    name: 'areaManager',
    type: 'api-select',
    placeholder: '请选择片区管理员',
    asyncFn: getUserList
  },
  {
    label: '单据状态',
    name: 'status',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_AuditStatus',
    placeholder: '请选择单据状态'
  },
  {
    label: '业务状态',
    name: 'bizStatus',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_LeaseUnit_BizStatus',
    placeholder: '请选择业务状态'
  },
  { label: '配套设施', name: 'supportFacility', type: 'input', placeholder: '请输入配套设施' },
  { label: '生效日期', name: 'effectDate', type: 'date', placeholder: '请选择生效日期' },
  { label: '到期日期', name: 'expireDate', type: 'date', placeholder: '请选择到期日期' },
  { label: '层数/总层数', name: 'layerNum', type: 'input', placeholder: '请输入层数' },
  {
    label: '产权用途',
    name: 'propertyUse',
    type: 'dict-select',
    code: 'CT_BAS_PropertyUse',
    placeholder: '请选择产权用途'
  },
  { label: '所属项目', name: 'wyProject', type: 'api-select', placeholder: '请选择所属项目', asyncFn: projectPage },
  { label: '关联资产', name: 'houseOwner', type: 'api-select', placeholder: '请选择关联资产', asyncFn: getPage },
  { label: '单元编码', name: 'number', type: 'input', placeholder: '请输入单元编码' }
])

const pageTitle = computed(() => route.meta.title)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getLeaseUnitList)
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const defaultColumns = [
  { title: '租赁单元名称', dataIndex: 'name', width: 200, fixed: 'left' },
  { title: '地址', dataIndex: 'detailAddress', width: 200, ellipsis: true },
  { title: '使用类型', dataIndex: 'useType_dictText', width: 120 },
  { title: '租赁面积(m²)', dataIndex: 'leaseArea', width: 120 },
  { title: '租赁用途', dataIndex: 'leaseUse_dictText', width: 120 },
  { title: '片区管理员', dataIndex: 'areaManager_dictText', width: 120, ellipsis: true },
  { title: '数据状态', dataIndex: 'status', width: 120 },
  { title: '业务状态', dataIndex: 'bizStatus_dictText', width: 120 },
  { title: '配套设施', dataIndex: 'supportFacility', width: 150, ellipsis: true },
  { title: '生效日期', dataIndex: 'effectDate', width: 120 },
  { title: '到期时间', dataIndex: 'expireDate', width: 120 },
  { title: '层数/总层数', dataIndex: 'layerNum', width: 120 },
  { title: '产权', dataIndex: 'property', width: 120 },
  { title: '项目', dataIndex: 'wyProject_dictText', width: 180, ellipsis: true },
  { title: '租赁单元编号', dataIndex: 'number', width: 200 },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const columns = computed(() => columnSetRef.value?.columns || defaultColumns)

/**
 * 打开新建租赁单元弹窗
 */
const handleAdd = () => {
  editDrawerRef.value.open()
}

/**
 * 打开编辑租赁单元弹窗
 */
const handleEdit = (row) => {
  editDrawerRef.value.open(row)
}

/**
 * 打开租赁单元详情弹窗
 */
const handleDetail = (record) => {
  detailDrawerRef.value.open(record)
}

/**
 * 打开导入弹窗
 */
const handleImport = () => {
  commonImportRef.value.open()
}

/**
 * 导出租赁单元数据
 */
const handleExport = async () => {
  if (exportLoading.value) return
  exportLoading.value = true
  try {
    await exportExcel('租赁单元清单.xls', searchParams)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}

/**
 * 删除单个租赁单元
 */
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除租赁单元"${record.name || record.number}"吗？此操作不可恢复！`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteLeaseUnit({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 批量删除租赁单元
 */
const handleBatchDelete = () => {
  if (!selectedRowKeys.value || selectedRowKeys.value.length === 0) {
    message.warning('请先选择要删除的租赁单元')
    return
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个租赁单元吗？此操作不可恢复！`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await batchDeleteLeaseUnit({ ids: selectedRowKeys.value.join(',') })
      message.success('批量删除成功')
      clearSelection()
      onTableChange()
    }
  })
}

/**
 * 执行反审批操作
 */
const handleUnAudit = (record) => {
  Modal.confirm({
    title: '确认反审批',
    content: `确定要对租赁单元"${record.name || record.number}"执行反审批操作吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await unAudit({ id: record.id })
      message.success('反审批成功')
      onTableChange()
    }
  })
}

/**
 * 表格变化事件处理
 */
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}

/**
 * 搜索输入防抖处理
 */
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({
      current: 1,
      pageSize: pagination.value.pageSize
    })
  }, 600)
}

/**
 * 处理树节点变更
 */
const handleTreeNodeChange = (nodeId) => {
  searchParams.treeId = nodeId
  onTableChange({ current: 1 })
}

onMounted(() => {
  // 资产页面跳转过来 （id:资产id）
  if (route.query.id) {
    searchParams.houseOwner = route.query.id
  }
  onTableChange()
})
</script>
