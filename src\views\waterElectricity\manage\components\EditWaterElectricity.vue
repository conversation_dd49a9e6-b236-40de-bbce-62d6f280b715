<template>
  <a-drawer
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}水电表`"
    class="common-drawer"
    width="1000px"
    :mask-closable="false"
  >
    <a-spin :spinning="confirmLoading">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 6 }">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="编码(表号)" name="number" required>
              <a-input v-model:value="formData.number" placeholder="请输入编码(表号)" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="名称" name="name" required>
              <a-input v-model:value="formData.name" placeholder="请输入名称" show-count :maxlength="50" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="倍率" name="doubleRate">
              <a-input-number
                v-model:value="formData.doubleRate"
                placeholder="请输入倍率"
                style="width: 100%"
                :min="0"
                :precision="2"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="类型" name="type" required>
              <dict-select
                v-model="formData.type"
                placeholder="请选择类型"
                code="CT_BASE_ENUM_WaterElectriCityTableNum_Type"
              ></dict-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="属性" name="property">
              <dict-select
                v-model="formData.property"
                placeholder="请选择属性"
                code="CT_BASE_ENUM_WaterElectriCityTableNum_Property"
              ></dict-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="分组" name="treeId">
              <api-tree-select
                v-model="formData.treeId"
                style="width: 100%"
                :async-fn="getWaterElectricityTableNumTree"
                placeholder="请选择分组"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="地址" name="address">
              <a-cascader
                v-model:value="formData.regionArray"
                placeholder="选择区域"
                class="pca-code"
                :options="region"
              />
              <a-input v-model:value="formData.address" placeholder="请输入详细地址" class="detail-address" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="租金归集公司" name="collectionCompany">
              <dept-tree-select
                v-model="formData.collectionCompany"
                placeholder="请选择租金归集公司"
                type="company"
              ></dept-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="资产权属公司" name="ownerCompany">
              <dept-tree-select
                v-model="formData.ownerCompany"
                placeholder="请选择资产权属公司"
                type="company"
              ></dept-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="物业管理公司" name="manageCompany">
              <dept-tree-select
                v-model="formData.manageCompany"
                placeholder="请选择物业管理公司"
                type="company"
              ></dept-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="单价" name="price" required>
              <a-input-number
                v-model:value="formData.price"
                placeholder="请输入单价"
                style="width: 100%"
                :min="0"
                :precision="2"
                addon-after="元"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" name="remark" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
              <a-textarea
                v-model:value="formData.remark"
                placeholder="请输入备注"
                :rows="4"
                show-count
                :maxlength="500"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSave">确认</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import region from '@/json/region.json'
import { addWaterElectricity, editWaterElectricity, queryById } from '../apis/waterElectricity'
import { getWaterElectricityTableNumTree } from '../apis/waterElectricityTableNumTree'

const emit = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()
const treeData = ref([])

const formDataDefault = reactive({
  id: undefined,
  number: undefined,
  name: undefined,
  status: 'ENABLE',
  doubleRate: undefined,
  type: '',
  property: '',
  treeId: '',
  address: undefined,
  price: undefined,
  collectionCompany: '',
  ownerCompany: '',
  manageCompany: '',
  remark: undefined,
  regionArray: []
})

const formData = reactive({ ...formDataDefault })

const rules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  number: [{ required: true, message: '请输入编码(表号)', trigger: 'blur' }],
  type: [{ required: true, message: '请选择类型', trigger: 'change' }],
  doubleRate: [{ type: 'number', min: 0.01, message: '倍率必须大于0', trigger: 'blur' }],
  address: [{ required: true, message: '请输入地址', trigger: 'blur' }],
  collectionCompany: [{ required: true, message: '请选择租金归集公司', trigger: 'change' }],
  ownerCompany: [{ required: true, message: '请选择资产权属公司', trigger: 'change' }],
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }],
  price: [{ type: 'number', min: 0.01, message: '单价必须大于0', trigger: 'blur' }]
}

/**
 * 打开抽屉弹窗
 * @param {Object} record 水电表记录，编辑时传入
 */
const open = async (record) => {
  visible.value = true
  confirmLoading.value = true

  try {
    if (record?.id) {
      const { result } = await queryById({ id: record.id })
      if (result) {
        Object.assign(formData, result)
      }
    }
  } finally {
    confirmLoading.value = false
  }
}

/**
 * 取消编辑并关闭抽屉
 */
const handleCancel = () => {
  formRef.value?.resetFields()
  Object.assign(formData, formDataDefault)
  visible.value = false
  emit('refresh')
}

/**
 * 保存水电表信息
 */
const handleSave = async () => {
  await formRef.value.validate()
  confirmLoading.value = true
  try {
    if (formData.id) {
      await editWaterElectricity(formData)
      message.success('编辑成功')
    } else {
      await addWaterElectricity(formData)
      message.success('添加成功')
    }
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 加载树形数据
 */
const loadTreeData = async () => {
  const { result } = await getWaterElectricityTableNumTree()
  if (result) {
    treeData.value = result
  }
}

onMounted(() => {
  loadTreeData()
})

defineExpose({ open })
</script>

<style scoped>
.pca-code,
.detail-address {
  width: 50%;
}

.pca-code :deep(.ant-select-selector) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: 0;
}
.detail-address {
  border-left: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
</style>
