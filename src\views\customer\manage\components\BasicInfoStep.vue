<template>
  <div>
    <h2 class="text-[16px] font-bold my-[24px]">客户基础信息</h2>
    <a-form ref="basicFormRef" :model="formData" :rules="basicRules" :label-col="{ span: 4 }">
      <a-form-item label="客户名称" name="name" required>
        <a-input v-model:value="formData.name" placeholder="请输入客户名称" show-count :maxlength="50" />
      </a-form-item>
      <a-form-item label="是否内部公司" name="isInternalCompany">
        <a-checkbox v-model:checked="formData.isInternalCompany" style="width: 10%">是</a-checkbox>
        <dept-tree-select
          v-if="formData.isInternalCompany"
          v-model="formData.internalCompany"
          placeholder="请选择公司"
          style="width: 90%"
          type="company"
        ></dept-tree-select>
      </a-form-item>
      <a-form-item label="管理公司" name="manageCompany" required>
        <dept-tree-select
          v-model="formData.manageCompany"
          placeholder="请选择管理公司"
          type="company"
        ></dept-tree-select>
      </a-form-item>
      <a-form-item label="客户来源" name="customerSource">
        <dict-select
          v-model="formData.customerSource"
          placeholder="客户来源"
          code="CT_BASE_ENUM_Customer_CustomerSource"
        ></dict-select>
      </a-form-item>
      <a-form-item label="客户类型" name="customerType">
        <dict-select
          v-model="formData.customerType"
          placeholder="客户类型"
          code="CT_BASE_ENUM_Customer_CustomerType"
        ></dict-select>
      </a-form-item>
      <a-form-item label="营业执照" name="busiLicence">
        <div class="flex items-center">
          <a-input v-model:value="formData.busiLicenceNum" placeholder="请输入营业执照号" style="flex: 1" />
          <div class="ml-2 min-w-[120px]">
            <!-- 上传按钮 -->
            <a-upload
              v-if="!busiLicenceFile.id"
              :custom-request="handleCustomUpload"
              :show-upload-list="false"
              accept=".pdf,.jpg,.jpeg,.png,.gif,.bmp"
              :before-upload="beforeUpload"
            >
              <a-button>上传</a-button>
            </a-upload>

            <!-- 上传成功状态 -->
            <div v-if="busiLicenceFile.id" class="flex items-center">
              <div class="flex items-center px-3 py-1 rounded border">
                <span class="text-sm text-tertiary max-w-[120px] truncate" :title="busiLicenceFile.name">
                  {{ busiLicenceFile.name }}
                </span>
                <a-upload
                  :custom-request="handleCustomUpload"
                  :show-upload-list="false"
                  accept=".pdf,.jpg,.jpeg,.png,.gif,.bmp"
                  :before-upload="beforeUpload"
                >
                  <i
                    class="a-icon-refresh ml-2 cursor-pointer text-blue-500 hover:text-blue-700 transition-colors"
                    title="重新上传"
                  ></i>
                </a-upload>
              </div>
            </div>
          </div>
        </div>
      </a-form-item>
      <a-form-item label="法人" name="legalPersonName">
        <a-input v-model:value="formData.legalPersonName" placeholder="请输入法人" />
      </a-form-item>
      <a-form-item label="法人身份证" name="legalPerson">
        <a-input v-model:value="formData.legalPerson" placeholder="请输入法人身份证" />
      </a-form-item>
      <a-form-item label="注册地址" name="registeredAddress">
        <a-cascader
          v-model:value="formData.registeredPcaCodeArray"
          placeholder="选择区域"
          class="pca-code"
          :options="region"
          @change="handlePcaCodeChange"
        />
        <a-input v-model:value="formData.registeredAddress" placeholder="请输入详细地址" class="detail-address" />
      </a-form-item>
      <a-form-item label="履约情况" name="performance">
        <dict-select v-model="formData.performance" placeholder="履约情况" code="CT_BAS_Performance"></dict-select>
      </a-form-item>
      <a-form-item label="安全等级" name="safeRate">
        <dict-select v-model="formData.safeRate" placeholder="安全等级" code="CT_BAS_SafeRate"></dict-select>
      </a-form-item>
    </a-form>
    <h2 class="text-[16px] font-bold my-[24px]">客户需求</h2>
    <a-form ref="requirementFormRef" :model="formData" :rules="requirementRules" :label-col="{ span: 4 }">
      <a-form-item label="维护日期" name="maintainDate" required>
        <a-date-picker v-model:value="formData.maintainDate" value-format="YYYY-MM-DD" style="width: 100%" />
      </a-form-item>
      <a-form-item label="维护人员" name="maintainPerson">
        <user-select v-model="formData.maintainPerson" placeholder="请选择维护人员" title="请选择维护人员" />
      </a-form-item>
      <a-form-item label="初步需求" name="initRequire" :label-col="{ span: 2 }" class="form-item-full">
        <a-textarea
          v-model:value="formData.initRequire"
          placeholder="请输入初步需求"
          :rows="4"
          show-count
          :maxlength="500"
        />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { message } from 'ant-design-vue'
import region from '@/json/region.json'
import { uploadFile, getAttachmentByIds } from '@/apis/common'

const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:loading'])

const basicFormRef = ref()
const requirementFormRef = ref()

// 营业执照上传相关
const busiLicenceFile = reactive({
  id: undefined,
  name: undefined
})

const basicRules = {
  name: [
    { required: true, message: '请输入客户名称', trigger: 'blur' },
    { min: 2, max: 50, message: '客户名称长度应为2-50个字符', trigger: 'blur' }
  ],
  manageCompany: [{ required: true, message: '请选择管理公司', trigger: 'change' }]
}

const requirementRules = {
  maintainDate: [{ required: true, message: '请选择维护日期', trigger: 'change' }]
}

/**
 * 处理省市区选择变化
 */
const handlePcaCodeChange = (value) => {
  props.formData.registeredPcaCode = value.join(',')
  props.formData.registeredPcaCodeArray = value
}

/**
 * 获取营业执照文件名
 */
const getBusiLicenceFileName = async (fileId) => {
  const attachmentData = await getAttachmentByIds(fileId)
  if (attachmentData && attachmentData.result && attachmentData.result.length > 0) {
    const fileInfo = attachmentData.result[0]
    busiLicenceFile.name = `${fileInfo.fileName}.${fileInfo.fileType}`
  }
}

/**
 * 上传前验证
 */
const beforeUpload = (file) => {
  // 文件类型验证
  const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp']
  if (!allowedTypes.includes(file.type)) {
    message.error('只支持上传 PDF 或图片格式文件（jpg、jpeg、png、gif、bmp）')
    return false
  }

  // 文件大小验证（限制5MB）
  const maxSize = 5 * 1024 * 1024
  if (file.size > maxSize) {
    message.error('文件大小不能超过 5MB')
    return false
  }

  return true
}

/**
 * 自定义上传处理
 */
const handleCustomUpload = async ({ file }) => {
  await uploadBusiLicenceFile(file)
}

/**
 * 上传营业执照文件
 */
const uploadBusiLicenceFile = async (file) => {
  try {
    busiLicenceFile.name = file.name

    // 设置加载状态
    emit('update:loading', true)

    // 创建 FormData
    const uploadFormData = new FormData()
    uploadFormData.append('file', file)
    uploadFormData.append('isAttachment', true)

    // 上传文件
    const response = await uploadFile(uploadFormData)

    busiLicenceFile.id = response.message
    props.formData.busiLicence = response.message
    message.success('营业执照上传成功')
  } catch {
    busiLicenceFile.id = undefined
    busiLicenceFile.name = undefined
  } finally {
    emit('update:loading', false)
  }
}

/**
 * 验证基础信息表单
 */
const validate = async () => {
  await Promise.all([basicFormRef.value?.validate(), requirementFormRef.value?.validate()])
}

/**
 * 重置表单
 */
const resetFields = () => {
  basicFormRef.value?.resetFields()
  requirementFormRef.value?.resetFields()
  busiLicenceFile.id = undefined
  busiLicenceFile.name = undefined
}

/**
 * 初始化营业执照文件状态
 */
const initBusiLicenceFile = (fileId) => {
  if (fileId) {
    busiLicenceFile.id = fileId
    busiLicenceFile.name = '营业执照文件' // 默认名称，异步获取真实文件名
    // 异步获取真实文件名
    getBusiLicenceFileName(fileId)
  }
}

watch(
  () => props.formData.busiLicence,
  (newVal) => {
    if (newVal) {
      initBusiLicenceFile(newVal)
    } else {
      busiLicenceFile.id = undefined
      busiLicenceFile.name = undefined
    }
  },
  { immediate: true }
)

defineExpose({
  validate,
  resetFields
})
</script>
