<template>
  <a-drawer
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}转账抵扣申请`"
    class="common-drawer"
    placement="right"
    width="1000px"
    :confirm-loading="confirmLoading"
    @close="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <h4 class="text-[16px] font-bold mb-[20px]">基础信息</h4>
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 4 }">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="业务时间" name="bizDate">
              <a-date-picker v-model:value="formData.bizDate" value-format="YYYY-MM-DD" style="width: 100%" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="经办人" name="operator">
              <user-select v-model="formData.operator" placeholder="请选择经办人" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="业务部门" name="operatorDepart">
              <dept-tree-select
                v-model:value="formData.operatorDepart"
                placeholder="请选择业务部门"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" name="remark" :label-col="{ span: 2 }">
              <a-textarea v-model:value="formData.remark" placeholder="请输入备注信息" :rows="4" />
            </a-form-item>
          </a-col>
        </a-row>

        <h4 class="text-[16px] font-bold my-[24px] flex justify-between">
          <span>
            转款明细
            <span class="text-[12px] text-tertiary">即：可用于抵扣的款项，转出用于抵扣其他未收取的应收款</span>
          </span>
          <a-button type="primary" @click="handleAddTransferItem">添加明细</a-button>
        </h4>
        <a-table
          :columns="transferColumns"
          :data-source="formData.transferItems"
          :pagination="false"
          size="middle"
          bordered
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <a-button type="link" danger @click="handleRemoveTransferItem(record)">删除</a-button>
            </template>
          </template>
        </a-table>

        <h4 class="text-[16px] font-bold my-[24px] flex justify-between">
          <span>
            抵扣欠款明细
            <span class="text-[12px] text-tertiary">即：上述转出明细的总转出金额要抵扣的未收取营收明细</span>
          </span>
          <a-button type="primary" @click="handleAddDebtItem">添加明细</a-button>
        </h4>
        <a-table :columns="debtColumns" :data-source="formData.debtItems" :pagination="false" size="middle" bordered>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <a-button type="link" danger @click="handleRemoveDebtItem(record)">删除</a-button>
            </template>
          </template>
        </a-table>
      </a-form>
      <div class="p-[16px] bg-[#f7f8fa] rounded-[8px] mt-[40px]">
        <p class="text-[24px] w-[100%]">转出合计：3000 转入合计：2000</p>
        <p class="w-[100%] mt-[8px] text-[22px] text-tertiary">已结清</p>
      </div>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { editTransferDeduction, addTransferDeduction, submitTransferDeduction } from '../apis'
import UserSelect from '@/components/UserSelect.vue'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()

const formDataDefault = {
  bizDate: undefined,
  operatorDepart: undefined,
  operator: undefined,
  remark: undefined,
  transferItems: [],
  debtItems: []
}
const formData = reactive({ ...formDataDefault })

const rules = {
  bizDate: [{ required: true, message: '请选择业务时间' }],
  operatorDepart: [{ required: true, message: '请选择业务部门' }],
  operator: [{ required: true, message: '请选择经办人' }]
}

const transferColumns = [
  { title: '收据编号', dataIndex: 'receiptNo', width: 200 },
  { title: '收款类型', dataIndex: 'type', width: 120 },
  { title: '转款金额', dataIndex: 'transferAmount', width: 120 },
  { title: '备注', dataIndex: 'remark' },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const debtColumns = [
  { title: '收据编号', dataIndex: 'receiptNo', width: 200 },
  { title: '收款类型', dataIndex: 'type', width: 120 },
  { title: '抵扣金额', dataIndex: 'transferAmount', width: 120 },
  { title: '备注', dataIndex: 'remark' },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

/**
 * 打开编辑弹窗
 * @param {Object} data - 编辑的数据对象，新建时为空
 */
const open = (data) => {
  if (data && data.id) {
    Object.assign(formData, data)
  }

  visible.value = true
}

/**
 * 关闭抽屉并重置表单
 */
const handleCancel = () => {
  Object.assign(formData, formDataDefault)
  emits('refresh')
  visible.value = false
}

/**
 * 添加转款明细行
 */
const handleAddTransferItem = () => {
  formData.transferItems.push({
    receiptNo: '',
    type: '',
    transferAmount: 0,
    remark: ''
  })
}

/**
 * 移除转款明细行
 * @param {number} index - 要移除的行索引
 */
const handleRemoveTransferItem = (index) => {
  formData.transferItems.splice(index, 1)
}

/**
 * 添加抵扣欠款明细行
 */
const handleAddDebtItem = () => {
  formData.debtItems.push({
    receiptNo: '',
    type: '',
    transferAmount: 0,
    remark: ''
  })
}

/**
 * 移除抵扣欠款明细行
 * @param {number} index - 要移除的行索引
 */
const handleRemoveDebtItem = (index) => {
  formData.debtItems.splice(index, 1)
}

/**
 * 保存表单数据
 * @param {boolean} isTemporary - 是否为暂存，true为暂存，false为提交
 */
const saveData = async (isTemporary = false) => {
  if (confirmLoading.value) return

  confirmLoading.value = true

  await formRef.value?.validate()

  // 根据操作类型选择对应的API
  const api = formData.id ? editTransferDeduction : isTemporary ? addTransferDeduction : submitTransferDeduction

  try {
    await api(formData)

    const action = isTemporary ? '暂存' : formData.id ? '编辑' : '添加'
    message.success(`转款抵扣申请${action}成功`)
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 提交表单
 */
const handleSubmit = () => saveData(false)

/**
 * 暂存表单
 */
const handleTemporaryStorage = () => saveData(true)

defineExpose({
  open
})
</script>
