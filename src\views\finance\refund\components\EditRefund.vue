<template>
  <a-drawer
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}退款申请`"
    class="common-drawer"
    placement="right"
    width="1000px"
  >
    <a-spin :spinning="confirmLoading">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 4 }">
        <h4 class="text-[16px] font-bold mb-[20px]">基础信息</h4>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="业务时间" name="bizDate">
              <a-date-picker v-model:value="formData.bizDate" value-format="YYYY-MM-DD" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="退款客户" name="customer">
              <customer-select v-model="formData.customer" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="经办人" name="operator">
              <user-select v-model="formData.operator" placeholder="请选择经办人" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="业务部门" name="operatorDepart">
              <dept-tree-select v-model="formData.operatorDepart" placeholder="请选择业务部门" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" name="remark" :label-col="{ span: 2 }">
              <a-textarea v-model:value="formData.remark" placeholder="请输入备注信息" :rows="4" />
            </a-form-item>
          </a-col>
        </a-row>

        <h4 class="text-[16px] font-bold my-[24px] flex justify-between">
          退款明细
          <a-button type="primary" @click="handleAddRefundItem">添加明细</a-button>
        </h4>
        <a-table
          :columns="refundColumns"
          :data-source="formData.refundItems"
          :pagination="false"
          :scroll="{ x: 1200 }"
          size="middle"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <a-button type="link" danger @click="handleRemoveRefundItem(record)">删除</a-button>
            </template>
          </template>
        </a-table>
      </a-form>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { addRefundReqBill, editRefundReqBill, submitRefundReqBill } from '../apis'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()

const formDataDefault = {
  bizDate: undefined,
  customer: undefined,
  operator: undefined,
  operatorDepart: undefined,
  remark: undefined,
  refundItems: []
}

const formData = reactive({ ...formDataDefault })

const rules = {
  bizDate: [{ required: true, message: '请选择业务日期' }],
  customer: [{ required: true, message: '请选择退款客户' }],
  operator: [{ required: true, message: '请选择经办人' }],
  operatorDepart: [{ required: true, message: '请选择业务部门' }]
}

const refundColumns = [
  { title: '账单编号', dataIndex: 'receiptNo', width: 200, fixed: 'left' },
  { title: '合同', dataIndex: 'type', width: 120 },
  { title: '款项类型', dataIndex: 'transferAmount', width: 120 },
  { title: '归属年月', dataIndex: 'transferAmount', width: 120 },
  { title: '期数/总期数', dataIndex: 'transferAmount', width: 120 },
  { title: '应收日期', dataIndex: 'transferAmount', width: 120 },
  { title: '剩余可退', dataIndex: 'transferAmount', width: 120, fixed: 'right' },
  { title: '本次退款金额', dataIndex: 'transferAmount', width: 120, fixed: 'right' },
  { title: '退款备注', dataIndex: 'remark', fixed: 'right' },
  { title: '操作', dataIndex: 'action', width: 100, fixed: 'right' }
]

/**
 * 打开抽屉并初始化表单数据
 * @param {Object} record - 编辑时的记录数据，新建时为空
 */
const open = (record) => {
  visible.value = true
  if (record?.id) {
    Object.assign(formData, record)
  }
}

/**
 * 取消操作，关闭抽屉并重置表单
 */
const handleCancel = () => {
  visible.value = false
  Object.assign(formData, formDataDefault)
  emits('refresh')
}

/**
 * 添加退款明细项
 */
const handleAddRefundItem = () => {
  // TODO: 实现添加退款明细逻辑
}

/**
 * 删除退款明细项
 * @param {Object} record - 要删除的明细记录
 */
const handleRemoveRefundItem = (record) => {
  const index = formData.refundItems.findIndex((item) => item === record)
  if (index > -1) {
    formData.refundItems.splice(index, 1)
  }
}

/**
 * 通用保存逻辑
 * @param {boolean} isTemporary - 是否为暂存（true: 暂存, false: 正式保存）
 */
const saveData = async (isTemporary = false) => {
  confirmLoading.value = true

  await formRef.value?.validate()
  // 根据操作类型选择对应的API
  const api = formData.id ? editRefundReqBill : isTemporary ? addRefundReqBill : submitRefundReqBill

  try {
    await api(formData)

    const action = isTemporary ? '暂存' : formData.id ? '编辑' : '添加'
    message.success(`退款申请${action}成功`)
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 提交退款申请
 */
const handleSubmit = () => saveData(false)

/**
 * 暂存退款申请
 */
const handleTemporaryStorage = () => saveData(true)

defineExpose({
  open
})
</script>
